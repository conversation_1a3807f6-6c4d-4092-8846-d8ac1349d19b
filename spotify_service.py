"""
Serviço para integração com a API do Spotify
Carrega playlists dinamicamente do usuário autenticado
"""

import os
import spotipy
from spotipy.oauth2 import SpotifyOAuth
from spotipy.cache_handler import FlaskSessionCacheHandler
import logging
from typing import List, Dict, Optional
from datetime import datetime, timedelta
from flask import session
import json

logger = logging.getLogger(__name__)


class SpotifyService:
    """Serviço para carregar playlists do Spotify usando a API oficial"""
    
    def __init__(self, session_cache_handler=None):
        self.client_id = os.getenv('SPOTIFY_CLIENT_ID')
        self.client_secret = os.getenv('SPOTIFY_CLIENT_SECRET')
        self.redirect_uri = os.getenv('SPOTIFY_REDIRECT_URL')
        self.scope = os.getenv('SPOTIFY_SCOPE', 'playlist-read-private playlist-read-collaborative')
        
        self.cache_handler = session_cache_handler or FlaskSessionCacheHandler(session)
        self.spotify = None
        self._cache = {}
        self._cache_expiry = {}
        self.cache_duration = timedelta(hours=1)  # Cache por 1 hora
        
        if not self.client_id:
            logger.error("SPOTIFY_CLIENT_ID não encontrada nas variáveis de ambiente")
            raise ValueError("SPOTIFY_CLIENT_ID é obrigatória")
            
        if not self.client_secret:
            logger.error("SPOTIFY_CLIENT_SECRET não encontrada nas variáveis de ambiente")
            raise ValueError("SPOTIFY_CLIENT_SECRET é obrigatória")
            
        if not self.redirect_uri:
            logger.error("SPOTIFY_REDIRECT_URL não encontrada nas variáveis de ambiente")
            raise ValueError("SPOTIFY_REDIRECT_URL é obrigatória")
        
        self.auth_manager = SpotifyOAuth(
            client_id=self.client_id,
            client_secret=self.client_secret,
            redirect_uri=self.redirect_uri,
            scope=self.scope,
            cache_handler=self.cache_handler,
            show_dialog=True
        )

    def _is_cache_valid(self, key: str) -> bool:
        """Verifica se o cache ainda é válido"""
        if key not in self._cache_expiry:
            return False
        return datetime.now() < self._cache_expiry[key]

    def _set_cache(self, key: str, data: any) -> None:
        """Define dados no cache com expiração"""
        self._cache[key] = data
        self._cache_expiry[key] = datetime.now() + self.cache_duration

    def _get_cache(self, key: str) -> Optional[any]:
        """Obtém dados do cache se válidos"""
        if self._is_cache_valid(key):
            return self._cache[key]
        return None

    def get_auth_url(self) -> str:
        """
        Retorna a URL de autorização do Spotify
        
        Returns:
            URL para autorização do usuário
        """
        return self.auth_manager.get_authorize_url()

    def get_access_token(self, code: str = None) -> Optional[str]:
        """
        Obtém o token de acesso do Spotify
        
        Args:
            code: Código de autorização (opcional)
            
        Returns:
            Token de acesso ou None se não autenticado
        """
        try:
            if code:
                # Trocar código por token
                token_info = self.auth_manager.get_access_token(code)
            else:
                # Verificar se já temos um token válido
                token_info = self.auth_manager.get_cached_token()
            
            if token_info:
                return token_info['access_token']
            return None
            
        except Exception as e:
            logger.error(f"Erro ao obter token de acesso: {e}")
            return None

    def is_authenticated(self) -> bool:
        """
        Verifica se o usuário está autenticado
        
        Returns:
            True se autenticado, False caso contrário
        """
        token = self.get_access_token()
        return token is not None

    def get_spotify_client(self) -> Optional[spotipy.Spotify]:
        """
        Obtém cliente autenticado do Spotify
        
        Returns:
            Cliente Spotify ou None se não autenticado
        """
        if not self.spotify:
            token = self.get_access_token()
            if token:
                self.spotify = spotipy.Spotify(auth=token)
            else:
                return None
        return self.spotify

    def get_user_playlists(self, max_results: int = 50) -> List[Dict]:
        """
        Carrega todas as playlists do usuário autenticado
        
        Args:
            max_results: Número máximo de playlists para retornar
            
        Returns:
            Lista de dicionários com informações das playlists
        """
        cache_key = f"user_playlists_{max_results}"
        
        # Verifica cache primeiro
        cached_data = self._get_cache(cache_key)
        if cached_data:
            logger.info("Retornando playlists do Spotify do cache")
            return cached_data

        spotify = self.get_spotify_client()
        if not spotify:
            logger.warning("Usuário não autenticado no Spotify")
            return []

        try:
            logger.info("Buscando playlists do usuário no Spotify")
            
            playlists = []
            offset = 0
            limit = min(50, max_results)  # Spotify API limit is 50
            
            while len(playlists) < max_results:
                # Ajustar limit para não exceder max_results
                current_limit = min(limit, max_results - len(playlists))
                
                results = spotify.current_user_playlists(
                    limit=current_limit,
                    offset=offset
                )
                
                if not results['items']:
                    break
                
                for item in results['items']:
                    # Incluir apenas playlists públicas ou do próprio usuário
                    if item['public'] or item['owner']['id'] == spotify.current_user()['id']:
                        playlist_data = self._format_playlist_data(item)
                        playlists.append(playlist_data)
                
                # Verificar se há mais páginas
                if not results['next']:
                    break
                    
                offset += current_limit
            
            logger.info(f"Encontradas {len(playlists)} playlists do Spotify")
            
            # Salva no cache
            self._set_cache(cache_key, playlists)
            
            return playlists
            
        except Exception as e:
            logger.error(f"Erro ao buscar playlists do Spotify: {e}")
            return []

    def _format_playlist_data(self, item: Dict) -> Dict:
        """
        Formata os dados da playlist para o formato esperado pelo frontend
        
        Args:
            item: Item da resposta da API do Spotify
            
        Returns:
            Dicionário formatado com dados da playlist
        """
        # Extrai thumbnail de melhor qualidade disponível
        thumbnail_url = None
        if item.get('images'):
            # Pegar a primeira imagem (geralmente a de melhor qualidade)
            thumbnail_url = item['images'][0]['url']
        
        return {
            'playlist_id': item['id'],
            'title': item.get('name', 'Sem título'),
            'description': item.get('description', ''),
            'thumbnail_url': thumbnail_url,
            'video_count': item.get('tracks', {}).get('total', 0),
            'url': item.get('external_urls', {}).get('spotify', ''),
            'embed_url': f"https://open.spotify.com/embed/playlist/{item['id']}",
            'owner': item.get('owner', {}).get('display_name', ''),
            'public': item.get('public', False),
            'collaborative': item.get('collaborative', False),
            'platform': 'spotify'
        }

    def get_playlist_details(self, playlist_id: str) -> Optional[Dict]:
        """
        Obtém detalhes específicos de uma playlist
        
        Args:
            playlist_id: ID da playlist
            
        Returns:
            Dicionário com detalhes da playlist ou None se não encontrada
        """
        cache_key = f"playlist_details_{playlist_id}"
        
        # Verifica cache primeiro
        cached_data = self._get_cache(cache_key)
        if cached_data:
            return cached_data

        spotify = self.get_spotify_client()
        if not spotify:
            return None

        try:
            playlist = spotify.playlist(playlist_id)
            playlist_data = self._format_playlist_data(playlist)
            
            # Salva no cache
            self._set_cache(cache_key, playlist_data)
            
            return playlist_data
            
        except Exception as e:
            logger.error(f"Erro ao buscar detalhes da playlist {playlist_id}: {e}")
            return None

    def search_playlists(self, query: str, max_results: int = 20) -> List[Dict]:
        """
        Busca playlists do usuário por termo de pesquisa
        
        Args:
            query: Termo de pesquisa
            max_results: Número máximo de resultados
            
        Returns:
            Lista de playlists que correspondem à pesquisa
        """
        if not query or len(query.strip()) < 2:
            return []
            
        # Busca todas as playlists e filtra localmente
        all_playlists = self.get_user_playlists(max_results=100)
        
        query_lower = query.lower().strip()
        filtered_playlists = []
        
        for playlist in all_playlists:
            # Busca no título e descrição
            title_match = query_lower in playlist.get('title', '').lower()
            desc_match = query_lower in playlist.get('description', '').lower()
            owner_match = query_lower in playlist.get('owner', '').lower()
            
            if title_match or desc_match or owner_match:
                filtered_playlists.append(playlist)
                
            if len(filtered_playlists) >= max_results:
                break
        
        logger.info(f"Encontradas {len(filtered_playlists)} playlists do Spotify para a busca '{query}'")
        return filtered_playlists

    def get_featured_playlist(self) -> Optional[Dict]:
        """
        Retorna uma playlist em destaque (a mais recente ou com mais músicas)
        
        Returns:
            Dicionário com dados da playlist em destaque ou None
        """
        cache_key = "featured_spotify_playlist"
        
        # Verifica cache primeiro
        cached_data = self._get_cache(cache_key)
        if cached_data:
            return cached_data

        playlists = self.get_user_playlists(max_results=20)
        
        if not playlists:
            return None
        
        # Ordena por número de músicas (descendente)
        featured = max(playlists, key=lambda p: p.get('video_count', 0))
        
        # Salva no cache
        self._set_cache(cache_key, featured)
        
        return featured

    def clear_cache(self) -> None:
        """Limpa todo o cache"""
        self._cache.clear()
        self._cache_expiry.clear()
        logger.info("Cache do Spotify limpo")

    def logout(self) -> None:
        """Remove tokens de autenticação"""
        try:
            self.cache_handler.cache_handler.clear()
            self.spotify = None
            self.clear_cache()
            logger.info("Usuário deslogado do Spotify")
        except Exception as e:
            logger.error(f"Erro ao fazer logout: {e}")


# Instância global do serviço
spotify_service = None

def get_spotify_service(session_cache_handler=None) -> SpotifyService:
    """
    Retorna a instância global do serviço do Spotify
    Cria uma nova instância se necessário
    """
    global spotify_service
    
    if spotify_service is None:
        try:
            spotify_service = SpotifyService(session_cache_handler)
        except Exception as e:
            logger.error(f"Erro ao criar serviço do Spotify: {e}")
            raise
    
    return spotify_service
