"""
YouTube API Service Module
Handles YouTube Data API v3 interactions for playlist management
"""

import os
import json
import logging
from typing import List, Dict, Optional
from datetime import datetime, timedelta
from googleapiclient.discovery import build
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import Flow
from googleapiclient.errors import HttpError


class YouTubeService:
    """Service class for YouTube API operations"""
    
    def __init__(self, api_key: str = None, channel_id: str = None):
        """
        Initialize YouTube service
        
        Args:
            api_key: YouTube Data API key
            channel_id: YouTube channel ID to fetch playlists from
        """
        self.api_key = api_key or os.getenv('YOUTUBE_API_KEY')
        self.channel_id = channel_id or os.getenv('YOUTUBE_CHANNEL_ID')
        self.youtube = None
        self._cache = {}
        self._cache_expiry = {}
        self.cache_duration = timedelta(hours=1)  # Cache for 1 hour
        
        if self.api_key:
            self._build_service()
        else:
            logging.warning("YouTube API key not provided")
    
    def _build_service(self):
        """Build YouTube service object"""
        try:
            self.youtube = build('youtube', 'v3', developer<PERSON>ey=self.api_key)
            logging.info("YouTube service initialized successfully")
        except Exception as e:
            logging.error(f"Failed to initialize YouTube service: {e}")
            self.youtube = None
    
    def _is_cache_valid(self, key: str) -> bool:
        """Check if cache entry is still valid"""
        if key not in self._cache_expiry:
            return False
        return datetime.now() < self._cache_expiry[key]
    
    def _set_cache(self, key: str, data: any):
        """Set cache entry with expiry"""
        self._cache[key] = data
        self._cache_expiry[key] = datetime.now() + self.cache_duration
    
    def _get_cache(self, key: str) -> any:
        """Get cache entry if valid"""
        if self._is_cache_valid(key):
            return self._cache[key]
        return None
    
    def get_channel_playlists(self, max_results: int = 50) -> List[Dict]:
        """
        Get all playlists from the configured channel
        
        Args:
            max_results: Maximum number of playlists to return
            
        Returns:
            List of playlist dictionaries
        """
        if not self.youtube or not self.channel_id:
            logging.error("YouTube service not properly configured")
            return []
        
        cache_key = f"playlists_{self.channel_id}_{max_results}"
        cached_data = self._get_cache(cache_key)
        if cached_data:
            logging.info("Returning cached playlist data")
            return cached_data
        
        try:
            playlists = []
            next_page_token = None
            
            while len(playlists) < max_results:
                request = self.youtube.playlists().list(
                    part='snippet,contentDetails,status',
                    channelId=self.channel_id,
                    maxResults=min(50, max_results - len(playlists)),
                    pageToken=next_page_token
                )
                
                response = request.execute()
                
                for item in response.get('items', []):
                    if item['status']['privacyStatus'] == 'public':
                        playlist_data = self._format_playlist_data(item)
                        playlists.append(playlist_data)
                
                next_page_token = response.get('nextPageToken')
                if not next_page_token:
                    break
            
            # Cache the results
            self._set_cache(cache_key, playlists)
            logging.info(f"Retrieved {len(playlists)} playlists from YouTube")
            return playlists
            
        except HttpError as e:
            logging.error(f"YouTube API error: {e}")
            return []
        except Exception as e:
            logging.error(f"Unexpected error fetching playlists: {e}")
            return []
    
    def get_playlist_details(self, playlist_id: str) -> Optional[Dict]:
        """
        Get detailed information about a specific playlist
        
        Args:
            playlist_id: YouTube playlist ID
            
        Returns:
            Playlist details dictionary or None
        """
        if not self.youtube:
            return None
        
        cache_key = f"playlist_details_{playlist_id}"
        cached_data = self._get_cache(cache_key)
        if cached_data:
            return cached_data
        
        try:
            request = self.youtube.playlists().list(
                part='snippet,contentDetails,status',
                id=playlist_id
            )
            
            response = request.execute()
            items = response.get('items', [])
            
            if items:
                playlist_data = self._format_playlist_data(items[0])
                self._set_cache(cache_key, playlist_data)
                return playlist_data
            
            return None
            
        except HttpError as e:
            logging.error(f"YouTube API error fetching playlist {playlist_id}: {e}")
            return None
        except Exception as e:
            logging.error(f"Unexpected error fetching playlist {playlist_id}: {e}")
            return None
    
    def get_playlist_videos(self, playlist_id: str, max_results: int = 10) -> List[Dict]:
        """
        Get videos from a specific playlist
        
        Args:
            playlist_id: YouTube playlist ID
            max_results: Maximum number of videos to return
            
        Returns:
            List of video dictionaries
        """
        if not self.youtube:
            return []
        
        cache_key = f"playlist_videos_{playlist_id}_{max_results}"
        cached_data = self._get_cache(cache_key)
        if cached_data:
            return cached_data
        
        try:
            videos = []
            next_page_token = None
            
            while len(videos) < max_results:
                request = self.youtube.playlistItems().list(
                    part='snippet,contentDetails',
                    playlistId=playlist_id,
                    maxResults=min(50, max_results - len(videos)),
                    pageToken=next_page_token
                )
                
                response = request.execute()
                
                for item in response.get('items', []):
                    video_data = self._format_video_data(item)
                    videos.append(video_data)
                
                next_page_token = response.get('nextPageToken')
                if not next_page_token:
                    break
            
            self._set_cache(cache_key, videos)
            return videos
            
        except HttpError as e:
            logging.error(f"YouTube API error fetching videos from playlist {playlist_id}: {e}")
            return []
        except Exception as e:
            logging.error(f"Unexpected error fetching videos from playlist {playlist_id}: {e}")
            return []
    
    def _format_playlist_data(self, item: Dict) -> Dict:
        """Format playlist data for consistent output"""
        snippet = item.get('snippet', {})
        content_details = item.get('contentDetails', {})
        
        # Get thumbnail URL (prefer higher quality)
        thumbnails = snippet.get('thumbnails', {})
        thumbnail_url = (
            thumbnails.get('maxres', {}).get('url') or
            thumbnails.get('high', {}).get('url') or
            thumbnails.get('medium', {}).get('url') or
            thumbnails.get('default', {}).get('url') or
            ''
        )
        
        return {
            'id': item.get('id'),
            'title': snippet.get('title', ''),
            'description': snippet.get('description', ''),
            'thumbnail_url': thumbnail_url,
            'video_count': content_details.get('itemCount', 0),
            'published_at': snippet.get('publishedAt', ''),
            'channel_title': snippet.get('channelTitle', ''),
            'url': f"https://www.youtube.com/playlist?list={item.get('id')}"
        }
    
    def _format_video_data(self, item: Dict) -> Dict:
        """Format video data for consistent output"""
        snippet = item.get('snippet', {})
        
        # Get thumbnail URL
        thumbnails = snippet.get('thumbnails', {})
        thumbnail_url = (
            thumbnails.get('maxres', {}).get('url') or
            thumbnails.get('high', {}).get('url') or
            thumbnails.get('medium', {}).get('url') or
            thumbnails.get('default', {}).get('url') or
            ''
        )
        
        return {
            'id': snippet.get('resourceId', {}).get('videoId'),
            'title': snippet.get('title', ''),
            'description': snippet.get('description', ''),
            'thumbnail_url': thumbnail_url,
            'published_at': snippet.get('publishedAt', ''),
            'channel_title': snippet.get('videoOwnerChannelTitle', ''),
            'url': f"https://www.youtube.com/watch?v={snippet.get('resourceId', {}).get('videoId')}"
        }
    
    def clear_cache(self):
        """Clear all cached data"""
        self._cache.clear()
        self._cache_expiry.clear()
        logging.info("YouTube service cache cleared")
