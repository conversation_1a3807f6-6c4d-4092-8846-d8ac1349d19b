#!/usr/bin/env python3
"""
Script de teste para verificar a integração com o Spotify API
"""

import os
import sys
from dotenv import load_dotenv

# Carrega variáveis de ambiente
load_dotenv()

def test_spotify_service():
    """Testa o serviço do Spotify"""
    try:
        from spotify_service import get_spotify_service
        from spotipy.cache_handler import FlaskSessionCacheHandler
        
        print("🔍 Testando serviço do Spotify...")
        
        # Verifica se as variáveis de ambiente estão configuradas
        client_id = os.getenv('SPOTIFY_CLIENT_ID')
        client_secret = os.getenv('SPOTIFY_CLIENT_SECRET')
        redirect_uri = os.getenv('SPOTIFY_REDIRECT_URL')
        
        if not client_id:
            print("❌ SPOTIFY_CLIENT_ID não encontrada no arquivo .env")
            return False
            
        if not client_secret:
            print("❌ SPOTIFY_CLIENT_SECRET não encontrada no arquivo .env")
            return False
            
        if not redirect_uri:
            print("❌ SPOTIFY_REDIRECT_URL não encontrada no arquivo .env")
            return False
            
        print(f"✅ Client ID configurado: {client_id[:10]}...")
        print(f"✅ Client Secret configurado: {client_secret[:10]}...")
        print(f"✅ Redirect URI configurado: {redirect_uri}")
        
        # Testa a criação do serviço
        # Simula uma sessão Flask vazia
        class MockSession(dict):
            pass
        
        mock_session = MockSession()
        cache_handler = FlaskSessionCacheHandler(mock_session)
        spotify_service = get_spotify_service(cache_handler)
        print("✅ Serviço do Spotify criado com sucesso")
        
        # Testa URL de autenticação
        auth_url = spotify_service.get_auth_url()
        print(f"✅ URL de autenticação gerada: {auth_url[:50]}...")
        
        # Verifica se não está autenticado (esperado sem token)
        is_authenticated = spotify_service.is_authenticated()
        if not is_authenticated:
            print("✅ Status de autenticação correto (não autenticado)")
        else:
            print("⚠️  Usuário já autenticado (token em cache)")
            
        return True
        
    except ImportError as e:
        print(f"❌ Erro ao importar módulo: {e}")
        return False
    except Exception as e:
        print(f"❌ Erro durante o teste: {e}")
        return False

def test_flask_routes():
    """Testa as rotas do Flask"""
    try:
        from app import create_app
        
        print("\n🌐 Testando rotas do Flask...")
        
        app = create_app()
        
        with app.test_client() as client:
            # Testa rota de status de autenticação
            response = client.get('/auth/spotify/status')
            if response.status_code == 200:
                data = response.get_json()
                print(f"✅ Rota /auth/spotify/status funcionando: {data.get('authenticated', False)}")
            else:
                print(f"❌ Rota /auth/spotify/status retornou {response.status_code}")
                
            # Testa rota de autenticação
            response = client.get('/auth/spotify')
            if response.status_code == 200:
                data = response.get_json()
                if 'auth_url' in data:
                    print("✅ Rota /auth/spotify funcionando")
                else:
                    print("❌ Rota /auth/spotify não retornou auth_url")
            else:
                print(f"❌ Rota /auth/spotify retornou {response.status_code}")
                
            # Testa rota de playlists do Spotify (deve retornar 401 - não autenticado)
            print("🎵 Testando rota /api/playlists/spotify...")
            response = client.get('/api/playlists/spotify')
            
            if response.status_code == 401:
                data = response.get_json()
                print("✅ API retornou 401 (não autenticado) como esperado")
                if 'auth_url' in data:
                    print("✅ API incluiu URL de autenticação")
            else:
                print(f"⚠️  API retornou status {response.status_code} (esperado 401)")
                    
        return True
        
    except Exception as e:
        print(f"❌ Erro ao testar Flask: {e}")
        return False

def test_integration():
    """Testa a integração completa"""
    print("\n🔗 Testando integração completa...")
    
    try:
        from app import create_app
        from spotify_service import get_spotify_service
        from spotipy.cache_handler import FlaskSessionCacheHandler
        
        app = create_app()
        
        with app.test_client() as client:
            with app.test_request_context():
                # Simula uma sessão Flask
                from flask import session
                
                # Testa criação do serviço dentro do contexto Flask
                cache_handler = FlaskSessionCacheHandler(session)
                spotify_service = get_spotify_service(cache_handler)
                
                print("✅ Serviço criado dentro do contexto Flask")
                
                # Testa se a URL de autenticação é válida
                auth_url = spotify_service.get_auth_url()
                if 'spotify.com' in auth_url and 'authorize' in auth_url:
                    print("✅ URL de autenticação válida")
                else:
                    print("❌ URL de autenticação inválida")
                    return False
                    
        return True
        
    except Exception as e:
        print(f"❌ Erro na integração: {e}")
        return False

def main():
    """Função principal"""
    print("🚀 Iniciando testes da integração Spotify...")
    print("=" * 50)
    
    # Testa serviço do Spotify
    spotify_ok = test_spotify_service()
    
    # Testa rotas do Flask
    flask_ok = test_flask_routes()
    
    # Testa integração
    integration_ok = test_integration()
    
    print("\n" + "=" * 50)
    print("📊 RESUMO DOS TESTES:")
    print(f"  Spotify Service: {'✅ OK' if spotify_ok else '❌ FALHOU'}")
    print(f"  Flask Routes: {'✅ OK' if flask_ok else '❌ FALHOU'}")
    print(f"  Integration: {'✅ OK' if integration_ok else '❌ FALHOU'}")
    
    if spotify_ok and flask_ok and integration_ok:
        print("\n🎉 Todos os testes passaram! A integração Spotify está funcionando.")
        print("\n📝 Próximos passos:")
        print("  1. Inicie o servidor: python app.py")
        print("  2. Acesse: http://localhost:5000/streaming")
        print("  3. Clique na aba 'Spotify'")
        print("  4. Clique em 'Conectar com Spotify' para autenticar")
        print("  5. Autorize o aplicativo no Spotify")
        print("  6. Suas playlists aparecerão automaticamente")
        
        print("\n🔐 Informações de autenticação:")
        print("  - O Spotify usa OAuth 2.0")
        print("  - Você será redirecionado para o Spotify para autorizar")
        print("  - Após autorização, voltará para a página de streaming")
        print("  - O token será salvo na sessão do navegador")
    else:
        print("\n⚠️  Alguns testes falharam. Verifique os erros acima.")
        
    return 0 if (spotify_ok and flask_ok and integration_ok) else 1

if __name__ == "__main__":
    sys.exit(main())
