{% extends "base.html" %} {% block content %}
<div class="container mx-auto px-4 py-8">
  <h1
    class="text-3xl font-bold mb-6 bg-gradient-to-r from-red-500 from-10% to-red-700 to-60% text-transparent bg-clip-text"
    data-translate="admin_dashboard"
  ></h1>

  {% with messages = get_flashed_messages(with_categories=true) %} {% if
  messages %} {% for category, message in messages %}
  <div
    class="p-4 mb-4 text-sm rounded-lg {% if category == 'error' %}bg-red-100 text-red-700{% else %}bg-green-100 text-green-700{% endif %}"
  >
    {{ message }}
  </div>
  {% endfor %} {% endif %} {% endwith %}

  <div class="flex space-x-4 mb-8">
    <a
      href="{{ url_for('add_update') }}"
      class="hBtn"
      data-translate="admin_add_update"
    ></a>
    <a
      href="{{ url_for('admin_playlists') }}"
      class="hBtn"
      data-translate="admin_manage_playlists"
    >Manage Playlists</a>
    <a
      href="{{ url_for('admin_logout') }}"
      class="px-5 py-2.5 bg-gray-500 text-white rounded text-center text-lg transition-all duration-500 ease-in-out hover:bg-gray-600"
      data-translate="admin_logout"
    >
    </a>
  </div>

  <h2
    class="text-2xl font-semibold mb-4 text-[var(--text-primary)]"
    data-translate="admin_manage_updates"
  ></h2>
  <div class="overflow-x-auto relative shadow-md sm:rounded-lg">
    <table class="w-full text-sm text-left text-[var(--text-primary)]">
      <thead class="text-xs uppercase bg-[var(--background-secondary)]">
        <tr>
          <th
            scope="col"
            class="py-3 px-6"
            data-translate="admin_title_field"
          ></th>
          <th
            scope="col"
            class="py-3 px-6"
            data-translate="admin_version_field"
          ></th>
          <th scope="col" class="py-3 px-6"></th>
          <th scope="col" class="py-3 px-6" data-translate="admin_actions"></th>
        </tr>
      </thead>
      <tbody>
        {% for update in updates %}
        <tr
          class="border-b border-[var(--border-color)] hover:bg-[var(--background-secondary)]"
        >
          <td class="py-4 px-6">{{ update['title'] }}</td>
          <td class="py-4 px-6">
            {% if update['version'] %}
            <span
              class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
            >
              {{ update['version'] }}
            </span>
            {% else %}
            <span class="text-gray-400 dark:text-gray-500 italic">-</span>
            {% endif %}
          </td>
          <td class="py-4 px-6">{{ update['date'].split(' ')[0] }}</td>
          <td class="py-4 px-6 flex space-x-2">
            <a
              href="{{ url_for('edit_update', id=update['id']) }}"
              class="font-medium text-red-600 hover:underline"
              data-translate="admin_edit_update"
            >
            </a>
            <form
              method="POST"
              action="{{ url_for('delete_update', id=update['id']) }}"
              class="inline"
            >
              <button
                type="submit"
                class="font-medium text-red-600 hover:underline"
                onclick="return confirm(document.querySelector('[data-translate=\'admin_delete_confirm\']').textContent)"
                data-translate="admin_delete_update"
              ></button>
            </form>
          </td>
        </tr>
        {% endfor %}
      </tbody>
    </table>
  </div>
  <!-- Hidden element for translation -->
  <div class="hidden" data-translate="admin_delete_confirm"></div>
</div>
{% endblock %}
