.error-container {
    @apply flex flex-col items-center justify-center min-h-screen;

    h1 {
        @apply text-6xl mb-4;
    }

    h2 {
        @apply text-2xl font-bold mb-4 text-[var(--text-primary)];
    }

    p {
        @apply text-lg text-[var(--text-secondary)] max-w-lg mx-auto mb-6;
    }

    .btn {
        @apply bg-gradient-to-r from-red-500 from-10% to-red-700 to-60% text-white px-5 py-2.5 rounded-lg font-bold text-lg transition-all duration-500 ease-in-out cursor-pointer inline-flex items-center;

        &:hover {
            @apply transform -translate-y-1 shadow-lg;
        }

        i {
            @apply mr-2;
        }
    }

    @keyframes float {
        0% {
            transform: translateY(0px);
        }
        50% {
            transform: translateY(-20px);
        }
        100% {
            transform: translateY(0px);
        }
    }

    .gradient-text {
        @apply bg-gradient-to-r from-red-500 from-10% to-red-700 to-60% text-transparent bg-clip-text;
        @apply animate-[float_6s_ease-in-out_infinite];
    }
}