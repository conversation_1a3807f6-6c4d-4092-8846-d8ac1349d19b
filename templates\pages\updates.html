{% extends "base.html" %} {% block content %}
<div class="updates-section">
  <div class="updates-container">
    <div class="header-content">
      <h1 data-translate="updatesTitle"></h1>
      <div class="description" data-translate="updatesDescription"></div>
      <div class="date">
        <span data-translate="lastUpdate"></span> {{ updates[0]['date'].split('
        ')[0] if updates else 'No updates' }}
      </div>
    </div>

    <div class="updates-grid">
      {% for update in updates %}
      <div class="update-card">
        <div class="card-header">
          <i class="fas fa-newspaper"></i>
          <h2>{{ update['title'] }}</h2>
        </div>
        {% if update['version'] %}
        <div class="version-badge">{{ update['version'] }}</div>
        {% endif %}
        <div class="update-date">
          <i class="fas fa-calendar-alt"></i>
          {{ update['date'].split(' ')[0] }}
        </div>
        <div class="update-content">{{ update['content']|safe }}</div>
        {% if update['preview_link'] %}
        <div class="update-preview">{{ update['preview_link']|safe }}</div>
        {% endif %}
      </div>
      {% endfor %} {% if not updates %}
      <div class="update-card">
        <div class="card-header">
          <i class="fas fa-exclamation-circle"></i>
          <h2>No Updates</h2>
        </div>
        <div class="update-content">
          <p>
            There are no updates available at this time. Check back later for
            new information.
          </p>
        </div>
      </div>
      {% endif %}
    </div>
  </div>
</div>
{% endblock %}
