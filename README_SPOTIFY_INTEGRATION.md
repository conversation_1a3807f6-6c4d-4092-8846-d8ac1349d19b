# 🎵 Integração Spotify - Sistema de Playlists Dinâmicas

Este documento explica como usar o sistema de carregamento dinâmico de playlists do Spotify implementado no projeto.

## 📋 Visão Geral

O sistema permite carregar automaticamente todas as playlists do usuário autenticado no Spotify, renderizá-las em HTML com estilos SCSS responsivos e oferecer funcionalidades como:

- ✅ Carregamento dinâmico via Spotify Web API
- ✅ Autenticação OAuth 2.0 segura
- ✅ Cache inteligente (1 hora de duração)
- ✅ Busca e filtros
- ✅ Paginação automática
- ✅ Lazy loading de players
- ✅ Sistema de favoritos
- ✅ Visualização em grade ou lista
- ✅ Design responsivo com SCSS

## 🚀 Como Usar

### 1. Configuração Inicial

As variáveis de ambiente já estão configuradas no arquivo `.env`:

```env
SPOTIFY_CLIENT_ID=2b63895ad175467cac8199ac113b0a11
SPOTIFY_CLIENT_SECRET=87962c6943c04c8999e656ff3907865e
SPOTIFY_REDIRECT_URL=http://127.0.0.1:5000/callback
SPOTIFY_SCOPE=playlist-read-private playlist-read-collaborative
```

### 2. Iniciar o Servidor

```bash
# Navegar para o diretório do projeto
cd /c/Users/<USER>/Documents/GitHub/iamshiuba

# Executar testes (opcional)
python test_spotify_integration.py

# Iniciar o servidor
python app.py
```

### 3. Processo de Autenticação

1. **Acesse a página**: http://localhost:5000/streaming
2. **Clique na aba "Spotify"**
3. **Clique em "Conectar com Spotify"**
4. **Autorize o aplicativo** no site do Spotify
5. **Retorne automaticamente** para a página com suas playlists

## 🔧 APIs Disponíveis

### Verificar Status de Autenticação
```
GET /auth/spotify/status
```

### Iniciar Autenticação
```
GET /auth/spotify
```

### Callback de Autenticação
```
GET /callback?code={authorization_code}
```

### Listar Playlists do Usuário
```
GET /api/playlists/spotify
GET /api/playlists/spotify?max_results=20
```

### Buscar Playlists
```
GET /api/playlists/spotify?q=termo_de_busca
```

### Detalhes de uma Playlist
```
GET /api/playlists/spotify/{playlist_id}
```

### Logout
```
GET /auth/spotify/logout
```

## 🔐 Fluxo de Autenticação OAuth 2.0

### 1. Solicitação de Autorização
```javascript
// O usuário clica em "Conectar com Spotify"
window.open('https://accounts.spotify.com/authorize?...', '_self');
```

### 2. Autorização no Spotify
- Usuário faz login no Spotify (se necessário)
- Autoriza o aplicativo a acessar suas playlists
- Spotify redireciona para `/callback` com código

### 3. Troca de Código por Token
```python
# Servidor troca o código por token de acesso
token_info = auth_manager.get_access_token(code)
```

### 4. Acesso às APIs
```python
# Token é usado para acessar APIs do Spotify
spotify = spotipy.Spotify(auth=token)
playlists = spotify.current_user_playlists()
```

## 📱 Interface do Usuário

### Página de Streaming (`/streaming`)

#### Estados da Interface:

1. **Não Autenticado**: Mostra tela de login com botão "Conectar com Spotify"
2. **Autenticado**: Mostra playlists do usuário com todas as funcionalidades

#### Funcionalidades dos Cards:

Cada playlist é exibida com:
- **Thumbnail**: Placeholder clicável para carregar o player
- **Título**: Nome da playlist
- **Informações**: Número de músicas, proprietário
- **Botão de favorito**: Coração para marcar/desmarcar
- **Link direto**: Botão para abrir no Spotify

## 🎨 Estilos SCSS

### Classes Específicas do Spotify

```scss
.auth-required {
  // Tela de autenticação necessária
  .auth-content {
    // Container do conteúdo de autenticação
    .auth-icon {
      // Ícone do Spotify (verde)
    }
    .auth-button {
      // Botão "Conectar com Spotify"
    }
  }
}

.placeholder-content.spotify {
  // Placeholder específico do Spotify
}

.action-button.spotify {
  // Botão "Abrir no Spotify" (verde)
}
```

## 🔄 Como Funciona

### 1. Verificação de Autenticação
```javascript
// JavaScript verifica se usuário está autenticado
const response = await fetch("/auth/spotify/status");
if (!data.authenticated) {
  // Mostra tela de login
}
```

### 2. Carregamento de Playlists
```javascript
// Após autenticação, carrega playlists
const response = await fetch("/api/playlists/spotify");
const data = await response.json();
```

### 3. Cache Inteligente
```python
# O serviço Python usa cache para otimizar performance
cache_duration = timedelta(hours=1)  # Cache por 1 hora
```

### 4. Lazy Loading
```javascript
// Players só são carregados quando o usuário clica
iframe.src = "https://open.spotify.com/embed/playlist/{id}";
```

## 📊 Estrutura de Dados

### Formato da Playlist (API Response)
```json
{
  "playlist_id": "37i9dQZF1DX0XUsuxWHRQd",
  "title": "RapCaviar",
  "description": "New music from Lil Baby, Gunna and more",
  "thumbnail_url": "https://i.scdn.co/image/...",
  "video_count": 50,
  "url": "https://open.spotify.com/playlist/37i9dQZF1DX0XUsuxWHRQd",
  "embed_url": "https://open.spotify.com/embed/playlist/37i9dQZF1DX0XUsuxWHRQd",
  "owner": "Spotify",
  "public": true,
  "collaborative": false,
  "platform": "spotify"
}
```

### Status de Autenticação
```json
{
  "authenticated": true,
  "user": {
    "id": "user123",
    "display_name": "João Silva"
  }
}
```

## 🛠️ Personalização

### Modificar Escopo de Permissões
```python
# Em spotify_service.py
self.scope = "playlist-read-private playlist-read-collaborative user-library-read"
```

### Adicionar Filtros Personalizados
```python
# Em spotify_service.py
def filter_by_owner(self, playlists, owner_name):
    return [p for p in playlists if p.get('owner') == owner_name]
```

### Customizar Interface de Autenticação
```scss
// Em static/scss/components/_streaming.scss
.auth-required .auth-content {
  // Seus estilos personalizados
}
```

## 🔍 Troubleshooting

### Erro: "Client ID não encontrado"
- Verifique se o arquivo `.env` existe
- Confirme se as credenciais estão corretas

### Erro: "Invalid redirect URI"
- Verifique se a URL de callback está registrada no Spotify Dashboard
- Confirme se a URL no `.env` está correta

### Erro: "Token expired"
```python
# O sistema renova automaticamente, mas você pode forçar:
spotify_service.clear_cache()
```

### Erro: "Insufficient client scope"
- Verifique se o escopo inclui as permissões necessárias
- Faça logout e autentique novamente

### Playlists não aparecem
- Verifique se as playlists são públicas ou do próprio usuário
- Confirme se o usuário tem playlists na conta

## 🔒 Segurança

### Dados Armazenados
- **Token de acesso**: Armazenado na sessão Flask (temporário)
- **Refresh token**: Usado para renovar automaticamente
- **Nenhum dado sensível**: Não armazenamos senhas ou dados pessoais

### Permissões Solicitadas
- `playlist-read-private`: Ler playlists privadas
- `playlist-read-collaborative`: Ler playlists colaborativas

### Boas Práticas Implementadas
- ✅ Tokens expiram automaticamente
- ✅ Cache com tempo limitado
- ✅ Validação de URLs de callback
- ✅ Tratamento de erros robusto

## 📈 Próximos Passos

1. **Integração com YouTube**: Sistema unificado de playlists
2. **Sincronização**: Sincronizar favoritos entre plataformas
3. **Analytics**: Rastrear playlists mais acessadas
4. **Playlists colaborativas**: Funcionalidades avançadas
5. **Modo offline**: Cache persistente

## 🤝 Contribuição

Para contribuir:
1. Faça um fork do projeto
2. Crie uma branch para sua feature
3. Implemente os testes
4. Envie um pull request

---

**Desenvolvido com ❤️ usando Python, Flask, Spotipy e Spotify Web API**
