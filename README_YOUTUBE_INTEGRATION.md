# 🎵 Integração YouTube - Sistema de Playlists Dinâmicas

Este documento explica como usar o sistema de carregamento dinâmico de playlists do YouTube implementado no projeto.

## 📋 Visão Geral

O sistema permite carregar automaticamente todas as playlists públicas do seu canal do YouTube, renderizá-las em HTML com estilos SCSS responsivos e oferecer funcionalidades como:

- ✅ Carregamento dinâmico via YouTube API v3
- ✅ Cache inteligente (1 hora de duração)
- ✅ Busca e filtros
- ✅ Paginação automática
- ✅ Lazy loading de vídeos
- ✅ Sistema de favoritos
- ✅ Visualização em grade ou lista
- ✅ Design responsivo com SCSS

## 🚀 Como Usar

### 1. Configuração Inicial

As variáveis de ambiente já estão configuradas no arquivo `.env`:

```env
YOUTUBE_API_KEY=***************************************
YOUTUBE_CHANNEL_ID=UClpyeDz-WB8TeBZ-iU1PigA
```

### 2. Iniciar o Servidor

```bash
# Navegar para o diretório do projeto
cd /c/Users/<USER>/Documents/GitHub/iamshiuba

# Instalar dependências (se necessário)
uv pip install -r requirements.txt

# Executar testes (opcional)
python test_youtube_integration.py

# Iniciar o servidor
python app.py
```

### 3. Acessar a Interface

Abra o navegador e acesse:
- **Página principal**: http://localhost:5000/
- **Página de streaming**: http://localhost:5000/streaming

## 🔧 APIs Disponíveis

### Listar Todas as Playlists
```
GET /api/playlists/youtube
GET /api/playlists/youtube?max_results=20
```

### Buscar Playlists
```
GET /api/playlists/youtube?q=termo_de_busca
```

### Playlist em Destaque
```
GET /api/playlists/highlight
```

### Detalhes de uma Playlist
```
GET /api/playlists/youtube/{playlist_id}
```

## 📱 Interface do Usuário

### Página de Streaming (`/streaming`)

A página oferece:

1. **Abas YouTube/Spotify**: Alterne entre plataformas
2. **Campo de busca**: Pesquise playlists por título
3. **Botão de visualização**: Alterne entre grade e lista
4. **Cards de playlist**: Com lazy loading e botão de favoritos
5. **Paginação**: Navegação automática entre páginas

### Funcionalidades dos Cards

Cada playlist é exibida com:
- **Thumbnail**: Placeholder clicável para carregar o player
- **Título**: Nome da playlist
- **Botão de favorito**: Coração para marcar/desmarcar
- **Link direto**: Botão para abrir no YouTube

## 🎨 Estilos SCSS

Os estilos estão organizados em:
- `static/scss/components/_streaming.scss`: Estilos principais
- Classes CSS responsivas com Tailwind
- Suporte a tema claro/escuro
- Animações suaves

### Classes Principais

```scss
.streaming-container    // Container principal das playlists
.playlist-item         // Card individual da playlist
.video-container       // Container do player/thumbnail
.playlist-info         // Informações da playlist
.favorite-button       // Botão de favorito
.action-button         // Botões de ação (YouTube, Spotify)
```

## 🔄 Como Funciona

### 1. Carregamento Inicial
```javascript
// O JavaScript carrega automaticamente as playlists
const response = await fetch("/api/playlists/youtube");
const data = await response.json();
```

### 2. Cache Inteligente
```python
# O serviço Python usa cache para otimizar performance
cache_duration = timedelta(hours=1)  # Cache por 1 hora
```

### 3. Lazy Loading
```javascript
// Vídeos só são carregados quando o usuário clica
loadButton.addEventListener("click", () => {
    const iframe = document.createElement("iframe");
    iframe.src = src;
    // ... configurações do iframe
});
```

## 📊 Estrutura de Dados

### Formato da Playlist (API Response)
```json
{
  "playlist_id": "PLxxxxx",
  "title": "Nome da Playlist",
  "description": "Descrição...",
  "thumbnail_url": "https://...",
  "video_count": 10,
  "url": "https://youtube.com/playlist?list=PLxxxxx",
  "embed_url": "https://youtube.com/embed/videoseries?list=PLxxxxx",
  "published_at": "2024-01-01T00:00:00Z",
  "platform": "youtube"
}
```

## 🛠️ Personalização

### Adicionar Novos Filtros
```python
# Em youtube_service.py
def filter_by_date(self, playlists, start_date, end_date):
    # Implementar filtro por data
    pass
```

### Modificar Estilos
```scss
// Em static/scss/components/_streaming.scss
.playlist-item {
    // Seus estilos personalizados
}
```

### Adicionar Novas APIs
```python
# Em app.py
@app.route("/api/playlists/custom")
def api_custom_playlists():
    # Sua implementação personalizada
    pass
```

## 🔍 Troubleshooting

### Erro: "YOUTUBE_API_KEY não encontrada"
- Verifique se o arquivo `.env` existe
- Confirme se a chave está correta

### Erro: "Nenhuma playlist encontrada"
- Verifique se o CHANNEL_ID está correto
- Confirme se há playlists públicas no canal

### Erro: "Quota exceeded"
- A API do YouTube tem limites diários
- Aguarde ou use uma nova chave

### Cache não atualiza
```python
# Limpar cache manualmente
youtube_service = get_youtube_service()
youtube_service.clear_cache()
```

## 📈 Próximos Passos

1. **Integração com Spotify**: Implementar API similar para Spotify
2. **Banco de dados**: Persistir favoritos e configurações
3. **Analytics**: Rastrear playlists mais acessadas
4. **PWA**: Transformar em Progressive Web App
5. **Temas**: Adicionar mais opções de personalização

## 🤝 Contribuição

Para contribuir:
1. Faça um fork do projeto
2. Crie uma branch para sua feature
3. Implemente os testes
4. Envie um pull request

---

**Desenvolvido com ❤️ usando Python, Flask, SCSS e YouTube API v3**
