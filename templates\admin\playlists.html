{% extends "base.html" %} {% block content %}
<div
  class="max-w-6xl mx-auto mt-10 p-6 bg-[var(--background-secondary)] rounded-lg shadow-md"
>
  <h1 data-translate="admin_manage_playlists"></h1>

  {% with messages = get_flashed_messages(with_categories=true) %} {% if
  messages %} {% for category, message in messages %}
  <div
    class="p-4 mb-4 text-sm rounded-lg {% if category == 'error' %}bg-red-100 text-red-700{% else %}bg-green-100 text-green-700{% endif %}"
  >
    {{ message }}
  </div>
  {% endfor %} {% endif %} {% endwith %}

  <div class="flex space-x-4 mb-8">
    <a
      href="{{ url_for('add_playlist') }}"
      class="hBtn"
      data-translate="admin_add_playlist"
    >
    </a>
    <a
      href="{{ url_for('admin_dashboard') }}"
      class="px-5 py-2.5 bg-gray-500 text-white rounded text-center text-lg transition-all duration-500 ease-in-out hover:bg-gray-600"
      data-translate="admin_back_to_dashboard"
    >
    </a>
  </div>

  <div class="overflow-x-auto relative shadow-md sm:rounded-lg">
    <table class="w-full text-sm text-left text-[var(--text-primary)]">
      <thead class="text-xs uppercase bg-[var(--background-secondary)]">
        <tr>
          <th
            scope="col"
            class="py-3 px-6"
            data-translate="admin_title_field"
          ></th>
          <th
            scope="col"
            class="py-3 px-6"
            data-translate="admin_platform_field"
          ></th>
          <th
            scope="col"
            class="py-3 px-6"
            data-translate="admin_highlight_field"
          ></th>
          <th
            scope="col"
            class="py-3 px-6"
            data-translate="admin_tags_field"
          ></th>
          <th scope="col" class="py-3 px-6" data-translate="admin_actions"></th>
        </tr>
      </thead>
      <tbody>
        {% for playlist in playlists %}
        <tr
          class="border-b border-[var(--border-color)] hover:bg-[var(--background-secondary)]"
        >
          <td class="py-4 px-6">{{ playlist['title'] }}</td>
          <td class="py-4 px-6">
            <span class="flex items-center">
              <i class="{{ playlist['platform_icon'] }} mr-2"></i>
              {{ playlist['platform_name'] }}
            </span>
          </td>
          <td class="py-4 px-6">
            {% if playlist['is_highlight'] %}
            <span
              class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
            >
              <i class="fas fa-star mr-1"></i> Highlight
            </span>
            {% else %}
            <span class="text-gray-400 dark:text-gray-500 italic">-</span>
            {% endif %}
          </td>
          <td class="py-4 px-6">
            {% if playlist['tags'] %} {% for tag in playlist['tags'] %}
            <span
              class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 mr-1 mb-1"
            >
              {{ tag }}
            </span>
            {% endfor %} {% else %}
            <span class="text-gray-400 dark:text-gray-500 italic">-</span>
            {% endif %}
          </td>
          <td class="py-4 px-6 flex space-x-2">
            <a
              href="{{ url_for('edit_playlist', id=playlist['id']) }}"
              class="font-medium text-red-600 hover:underline"
              data-translate="admin_edit"
            >
            </a>
            <form
              method="POST"
              action="{{ url_for('delete_playlist', id=playlist['id']) }}"
              class="inline"
              onsubmit="return confirm('Are you sure you want to delete this playlist?');"
            >
              <button
                type="submit"
                class="font-medium text-red-600 hover:underline"
                data-translate="admin_delete"
              ></button>
            </form>
          </td>
        </tr>
        {% endfor %} {% if not playlists %}
        <tr class="border-b border-[var(--border-color)]">
          <td colspan="5" class="py-4 px-6 text-center italic">
            <span data-translate="admin_no_playlists"></span>
          </td>
        </tr>
        {% endif %}
      </tbody>
    </table>
  </div>
</div>
{% endblock %}
